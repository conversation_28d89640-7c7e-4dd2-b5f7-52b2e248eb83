from __future__ import annotations
import asyncio
import json
import sys

from typing import Optional, List
from contextlib import AsyncExitStack
from urllib.parse import urlparse

from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client
from mcp.types import TextContent

from openai import Async<PERSON>penA<PERSON>
from openai.types.chat import ChatCompletion

from dotenv import load_dotenv
from openai.types.chat.chat_completion_tool_param import ChatCompletionToolParam

load_dotenv()  # Load environment variables from .env file

MODEL = "gpt-4.1"
SYSTEM_PROMPT = """You are an intelligent AI assistant with access to a set of tools. You can AUTOMATICALLY PERFORM INPUT TRANSFORMATION that enable you to provide an exceptional user experience.

CRITICAL: INPUT TRANSFORMATIONS HAPPEN AUTOMATICALLY -> Be smart enough to analyze the user inputs and transform them to the correct format.
- Phone numbers, dates, times, and other inputs are automatically transformed to the correct format BEFORE tools are called
- You do NOT need to mention formatting, transformation, or ask users to reformat anything
- Simply proceed naturally with the conversation - the system handles all formatting behind the scenes
- NEVER mention "formatting", "transforming", or "adjusting" user input - it happens invisibly
- You must adhere to the input schema of each tools. **This is a MUST**
- ONLY call `get_schedule_tour_link` if the user requests to re-schedule a tour. Otherwise, call `schedule_tour`
- **If you say `ther's a technical issue`, tell the user what the issue is.** -> Be smart enough to analyze the user inputs and transform them to the correct format.

## Important
- If there is a missing or incorrect information:
  - Display the information you currently have
  - Ask the user for the correct information
  - Ask for the missing information
  - Then call the tool again after the user provides the correct information
- If for example the user already gave you a date and the tool requires another date input, analyze the required input and see if you can use the previously given date as the required input
- If you already have all the necessary information to call a tool, call the tool immediately. DO NOT re-confirm the information with the user.
  
## Marketing Source
- If there is no marketing source, set the marketing source as follows:
    ```json
    "marketing_source": {
      "code": "direct",
      "domain": "direct"
    }
    ```

## Input Transformation
- You MUST transform `requested_times` to the following format:
    ```json
    "requested_times": [
      {
        "start_time": "2025-08-20T20:30:00-05:00"
      }
    ]
    ```
- You MUST transform `phone_number` to the following format:
    ```json
    "profile": {
      ...
      "phone_number": "(*************"
    }
    ```
    
## Pets
- If user has no pets, set the pets params as follows:
    ```json
    "profile": {
      ...
      "pets": {
          "none": true,
          "large_dogs": false,
          "small_dogs": false,
          "cats": false
      }
    },
    ```
- If user has cats, pass `cats: true` in the `pets` object
- If user has small dogs, pass `small_dogs: true` in the `pets` object
- If user has large dogs, pass `large_dogs: true` in the `pets` object
"""


class GPTClient:

    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.openai = AsyncOpenAI()
        self.conversation_history = []
        self.server_url = ""

    async def connect(self, server_url: str):
        """Connect to MCP server over HTTP transport

        Args:
            server_url: HTTP URL of the MCP server (e.g., 'http://localhost:8000/mcp')
        """
        try:
            parsed = urlparse(server_url)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError(f"Invalid server URL: {server_url}")
            if parsed.scheme not in ("http", "https"):
                raise ValueError("URL scheme must be http or https")

            self.server_url = server_url
        except Exception as e:
            raise ValueError(f"Invalid server URL '{server_url}': {e}")

        try:
            print("Establishing HTTP transport connection...")
            http_transport = await asyncio.wait_for(
                self.exit_stack.enter_async_context(streamablehttp_client(server_url)), timeout=30.0
            )
            self.read_stream, self.write_stream, self.session_id = http_transport

            print("Creating MCP session...")
            self.session = await self.exit_stack.enter_async_context(ClientSession(self.read_stream, self.write_stream))

            print("Initializing session...")
            await asyncio.wait_for(
                self.session.initialize(), timeout=10.0
            )  # Wait for initialization to complete within 10 seconds

            print("Discovering available tools...")
            # toolsData = await self.session.list_tools()
            # tools = toolsData.tools
            print(f"\n✅ Connected to MCP server at {server_url}")
            # print(f"📋 Available tools: {json.dumps([tool.name for tool in tools], indent=2)}")

            # for tool in tools:
            #     if tool.name == 'schedule_tour':
            #         print(f"Schedule tour tool schema: {json.dumps(tool.inputSchema, indent=2)}")

            # if self.session_id:
            #     print(f"🔗 Session ID: {self.session_id}")

        except Exception as e:
            raise RuntimeError(f"Failed to connect to MCP server at {server_url}: {e}")

    async def disconnect(self):
        try:
            if self.exit_stack:
                print("Disconnecting from MCP server...")
                await self.exit_stack.aclose()
                print("✅ Disconnected successfully")
        except Exception as e:
            print(f"⚠️  Warning during disconnect: {e}")

    def clear_conversation_history(self):
        """Clear the conversation history to start fresh."""
        self.conversation_history.clear()
        print("🔄 Conversation history cleared")

    async def query(self, query: str) -> str:
        """Process query using openai and available tools"""
        self.conversation_history.append(
            {
                "role": "user",
                "content": query,
            }
        )
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
            *self.conversation_history,
        ]

        if not self.session:
            raise RuntimeError("Not connected to MCP server. Call connect() first.")

        toolData = await self.session.list_tools()
        tools: List[ChatCompletionToolParam] = [
            {
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description or "",
                    "parameters": tool.inputSchema,
                },
            }
            for tool in toolData.tools
        ]
        # print(f"Available tools: {json.dumps(tools, indent=2)}")

        try:
            print(f"Messages: {json.dumps(messages, indent=2)}")
            response: ChatCompletion = await self.openai.chat.completions.create(
                model=MODEL,
                messages=messages,
                tools=tools,
                parallel_tool_calls=True,
            )
        except Exception as e:
            raise RuntimeError(f"Failed to get response from OpenAI: {e}") from e

        final_text = []

        choice = response.choices[0]

        if choice.finish_reason == "tool_calls":
            tc_msg = choice.message
            messages.append(tc_msg)
            if tc_msg.tool_calls:
                for tool_call in tc_msg.tool_calls:
                    name = tool_call.function.name

                    try:
                        args = json.loads(tool_call.function.arguments) if tool_call.function.arguments else {}
                        # args = json.loads(tool_call.function.arguments)
                    except json.JSONDecodeError as e:
                        print(f"⚠️  Warning: Failed to parse tool arguments for {name}: {e}")
                        args = {}
                    except Exception as e:
                        print(f"⚠️  Warning: Failed to parse tool arguments for {name}: {e}")
                        args = {}

                    if not isinstance(args, dict):
                        print(f"⚠️  Warning: Tool arguments for {name} is not a dictionary: {args}")
                        args = {}

                    result = await self.session.call_tool(name, args)
                    final_text.append(f"[Calling tool {name} with arguments {json.dumps(args, indent=2)}]")

                    # Ensure content is a string
                    if isinstance(result, TextContent):
                        tool_output = result.content
                    elif isinstance(result, (dict, list)):
                        tool_output = json.dumps(result)
                    else:
                        tool_output = str(result)

                    print(f"Tool output[{name}]: {tool_output}")
                    messages.append(
                        {
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "content": tool_output,
                        }
                    )

                final = await self.openai.chat.completions.create(
                    model=MODEL,
                    messages=messages,
                )

                final_text.append(final.choices[0].message.content)

            else:
                final_text.append(choice.message.content)
        else:
            final_text.append(choice.message.content)

        response_str = "\n".join(final_text)
        self.conversation_history.append(
            {
                "role": "assistant",
                "content": response_str,
            }
        )
        print(f"Final Result: {response_str!r}")
        return response_str

    async def chat(self):
        while True:
            try:
                query = input("> ").strip()
                if query.lower() in ("quit", "exit", "q"):
                    print("Alright then, Goodbye!")
                    break

                if query.lower() == "clear":
                    self.conversation_history.clear()
                    continue

                if not query:
                    continue

                print("🤔 GPT is thinking...")
                response = await self.query(query)
                print("\n" + "=" * 50)
                print("🤖 GPT's response:")
                print("=" * 50)
                print(response)
                print("=" * 50 + "\n")

            except KeyboardInterrupt:
                print("\n\nChat interrupted. Type 'quit' to exit gracefully.")
                break
            except ConnectionError as e:
                print(f"\n❌ Connection error: {e}")
                print("The MCP server may have disconnected. Please restart the client.")
                break
            except Exception as e:
                print(f"\n❌ Error: {str(e)}")


async def main():
    if len(sys.argv) == 1:
        server_url = "http://localhost:8000/mcp"
        print(f"No server URL provided, using default: {server_url}")
    elif len(sys.argv) == 2:
        server_url = sys.argv[1]
    else:
        print("Usage: python gptclient.py [server_url]")
        print("Examples:")
        print("  python gptclient.py")
        print("  python gptclient.py http://localhost:8000/mcp")
        print("  python gptclient.py https://your-server.com/mcp")
        sys.exit(1)

    client = GPTClient()
    try:
        print(f"Connecting to MCP server at {server_url}...")
        await client.connect(server_url)
        await client.chat()
    except KeyboardInterrupt:
        print("\n\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure the MCP server is running")
        print("2. Verify the server URL is correct")
        print("3. Check that the server supports HTTP transport")
        print("4. Ensure your ANTHROPIC_API_KEY environment variable is set")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
