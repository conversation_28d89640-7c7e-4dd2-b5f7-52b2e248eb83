#!/usr/bin/env python3
"""
Test script to verify the JSON Schema resolution fix
"""

import asyncio
import json
import sys
from contextlib import AsyncExitStack

from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client

# Import the schema resolver
sys.path.insert(0, '.')
from client_openai import resolve_json_schema_refs


async def test_schema_resolution():
    """Test that the schema resolution works correctly"""
    server_url = "http://localhost:8000/mcp"
    
    async with AsyncExitStack() as stack:
        try:
            # Connect to MCP server
            print(f"Connecting to MCP server at {server_url}...")
            http_transport = await stack.enter_async_context(streamablehttp_client(server_url))
            read_stream, write_stream, session_id = http_transport
            
            # Create MCP session
            session = await stack.enter_async_context(ClientSession(read_stream, write_stream))
            
            # Initialize the session
            await session.initialize()
            
            # List available tools
            print("Discovering available tools...")
            tools_data = await session.list_tools()
            tools = tools_data.tools
            
            # Find the schedule_tour tool
            schedule_tour_tool = None
            for tool in tools:
                if tool.name == "schedule_tour":
                    schedule_tour_tool = tool
                    break
            
            if not schedule_tour_tool:
                print("❌ schedule_tour tool not found!")
                return False
            
            print(f"\n{'='*60}")
            print("TESTING SCHEMA RESOLUTION")
            print(f"{'='*60}")
            
            # Test the original schema
            original_schema = schedule_tour_tool.inputSchema
            print(f"\nOriginal schema has $defs: {'$defs' in original_schema}")
            print(f"Original schema $defs keys: {list(original_schema.get('$defs', {}).keys())}")
            
            # Test the resolved schema
            resolved_schema = resolve_json_schema_refs(original_schema)
            print(f"\nResolved schema has $defs: {'$defs' in resolved_schema}")
            
            # Check if the main structure is preserved
            print(f"\nOriginal top-level properties: {list(original_schema.get('properties', {}).keys())}")
            print(f"Resolved top-level properties: {list(resolved_schema.get('properties', {}).keys())}")
            
            # Check if the request property is properly resolved
            if 'properties' in resolved_schema and 'request' in resolved_schema['properties']:
                request_prop = resolved_schema['properties']['request']
                print(f"\nRequest property type: {request_prop.get('type', 'unknown')}")
                
                if 'properties' in request_prop and 'appointment_info' in request_prop['properties']:
                    appointment_info = request_prop['properties']['appointment_info']
                    print(f"AppointmentInfo type: {appointment_info.get('type', 'unknown')}")
                    
                    if 'properties' in appointment_info:
                        print(f"AppointmentInfo properties: {list(appointment_info['properties'].keys())}")
                        
                        # Check if profile is properly resolved
                        if 'profile' in appointment_info['properties']:
                            profile = appointment_info['properties']['profile']
                            print(f"Profile type: {profile.get('type', 'unknown')}")
                            if 'properties' in profile:
                                print(f"Profile properties: {list(profile['properties'].keys())}")
                                
                                # Check if pets is properly resolved
                                if 'pets' in profile['properties']:
                                    pets = profile['properties']['pets']
                                    print(f"Pets type: {pets.get('type', 'unknown')}")
                                    if 'properties' in pets:
                                        print(f"Pets properties: {list(pets['properties'].keys())}")
                                        
                                        # Verify all required pet fields are present
                                        expected_pet_fields = ['cats', 'large_dogs', 'none', 'small_dogs']
                                        actual_pet_fields = list(pets['properties'].keys())
                                        missing_fields = set(expected_pet_fields) - set(actual_pet_fields)
                                        if missing_fields:
                                            print(f"❌ Missing pet fields: {missing_fields}")
                                            return False
                                        else:
                                            print(f"✅ All pet fields present: {actual_pet_fields}")
            
            # Save the resolved schema for inspection
            with open('resolved_schedule_tour_schema.json', 'w') as f:
                json.dump(resolved_schema, f, indent=2)
            print(f"\n✅ Resolved schema saved to 'resolved_schedule_tour_schema.json'")
            
            # Test that the resolved schema is valid JSON
            try:
                json.dumps(resolved_schema)
                print("✅ Resolved schema is valid JSON")
            except Exception as e:
                print(f"❌ Resolved schema is not valid JSON: {e}")
                return False
            
            print(f"\n{'='*60}")
            print("SCHEMA RESOLUTION TEST PASSED")
            print(f"{'='*60}")
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
            return False


if __name__ == "__main__":
    success = asyncio.run(test_schema_resolution())
    sys.exit(0 if success else 1)
