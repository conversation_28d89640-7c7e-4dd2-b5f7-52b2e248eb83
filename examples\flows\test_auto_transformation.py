#!/usr/bin/env python3
"""
Test script for automatic input transformation in the enhanced OpenAI client.

This script tests that the _apply_input_transformations method works correctly.
"""

import sys
from pathlib import Path

# Add the examples directory to the path
examples_dir = Path(__file__).parent.parent
sys.path.insert(0, str(examples_dir))

try:
    from client_openai import MCPClient
    print("✅ Enhanced OpenAI client imported successfully")
except ImportError as e:
    print(f"❌ Failed to import enhanced OpenAI client: {e}")
    sys.exit(1)


def test_phone_transformation():
    """Test automatic phone number transformation."""
    print("\n=== Testing Automatic Phone Number Transformation ===")
    
    client = MCPClient()
    
    # Test data with various phone formats
    test_cases = [
        {
            "appointment_info": {
                "property_id": 123,
                "profile": {
                    "first_name": "<PERSON>",
                    "last_name": "<PERSON>",
                    "email": "<EMAIL>",
                    "phone_number": "************"  # Needs transformation
                }
            }
        },
        {
            "appointment_info": {
                "property_id": 123,
                "profile": {
                    "first_name": "<PERSON>",
                    "last_name": "<PERSON><PERSON>", 
                    "email": "<EMAIL>",
                    "phone_number": "5551234567"  # Needs transformation
                }
            }
        },
        {
            "appointment_info": {
                "property_id": 123,
                "profile": {
                    "first_name": "Bob",
                    "last_name": "<PERSON>",
                    "email": "<EMAIL>", 
                    "phone_number": "(*************"  # Already formatted
                }
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest Case {i}:")
        original_phone = test_case["appointment_info"]["profile"]["phone_number"]
        print(f"  Original: {original_phone}")
        
        # Apply transformations
        transformed = client._apply_input_transformations("schedule_tour", test_case)
        transformed_phone = transformed["appointment_info"]["profile"]["phone_number"]
        print(f"  Transformed: {transformed_phone}")
        
        # Verify transformation
        if client._is_already_formatted_phone(transformed_phone):
            print(f"  ✅ Correctly formatted")
        else:
            print(f"  ❌ Not properly formatted")


def test_datetime_transformation():
    """Test automatic datetime transformation."""
    print("\n=== Testing Automatic DateTime Transformation ===")
    
    client = MCPClient()
    
    # Test data with various datetime formats
    test_cases = [
        {
            "appointment_info": {
                "property_id": 123,
                "requested_times": [
                    {"start_time": "tomorrow at 2 PM"}  # Needs transformation
                ]
            }
        },
        {
            "appointment_info": {
                "property_id": 123,
                "requested_times": [
                    {"start_time": "2025-07-21T14:00:00-05:00"}  # Already formatted
                ]
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest Case {i}:")
        original_time = test_case["appointment_info"]["requested_times"][0]["start_time"]
        print(f"  Original: {original_time}")
        
        # Apply transformations
        transformed = client._apply_input_transformations("schedule_tour", test_case)
        transformed_time = transformed["appointment_info"]["requested_times"][0]["start_time"]
        print(f"  Transformed: {transformed_time}")
        
        # Verify transformation
        if client._is_already_formatted_datetime(transformed_time):
            print(f"  ✅ Correctly formatted")
        else:
            print(f"  ❌ Not properly formatted")


def test_move_date_transformation():
    """Test automatic move date transformation."""
    print("\n=== Testing Automatic Move Date Transformation ===")
    
    client = MCPClient()
    
    # Test data with various move date formats
    test_cases = [
        {
            "appointment_info": {
                "property_id": 123,
                "profile": {
                    "first_name": "John",
                    "last_name": "Smith",
                    "email": "<EMAIL>",
                    "target_move_date": "next month"  # Needs transformation
                }
            }
        },
        {
            "appointment_info": {
                "property_id": 123,
                "profile": {
                    "first_name": "Jane",
                    "last_name": "Doe",
                    "email": "<EMAIL>",
                    "target_move_date": "2025-08-01"  # Already formatted
                }
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest Case {i}:")
        original_date = test_case["appointment_info"]["profile"]["target_move_date"]
        print(f"  Original: {original_date}")
        
        # Apply transformations
        transformed = client._apply_input_transformations("schedule_tour", test_case)
        transformed_date = transformed["appointment_info"]["profile"]["target_move_date"]
        print(f"  Transformed: {transformed_date}")
        
        # Verify transformation
        if client._is_already_formatted_date(transformed_date):
            print(f"  ✅ Correctly formatted")
        else:
            print(f"  ❌ Not properly formatted")


def test_complete_transformation():
    """Test complete transformation with all input types."""
    print("\n=== Testing Complete Transformation ===")
    
    client = MCPClient()
    
    # Test data with all transformation types
    test_case = {
        "appointment_info": {
            "property_id": 123,
            "requested_times": [
                {"start_time": "tomorrow at 2 PM"}
            ],
            "profile": {
                "first_name": "John",
                "last_name": "Smith",
                "email": "<EMAIL>",
                "phone_number": "************",
                "target_move_date": "next month"
            }
        }
    }
    
    print("Original data:")
    print(f"  Phone: {test_case['appointment_info']['profile']['phone_number']}")
    print(f"  DateTime: {test_case['appointment_info']['requested_times'][0]['start_time']}")
    print(f"  Move Date: {test_case['appointment_info']['profile']['target_move_date']}")
    
    # Apply transformations
    transformed = client._apply_input_transformations("schedule_tour", test_case)
    
    print("\nTransformed data:")
    print(f"  Phone: {transformed['appointment_info']['profile']['phone_number']}")
    print(f"  DateTime: {transformed['appointment_info']['requested_times'][0]['start_time']}")
    print(f"  Move Date: {transformed['appointment_info']['profile']['target_move_date']}")
    
    # Verify all transformations
    phone_ok = client._is_already_formatted_phone(transformed['appointment_info']['profile']['phone_number'])
    datetime_ok = client._is_already_formatted_datetime(transformed['appointment_info']['requested_times'][0]['start_time'])
    date_ok = client._is_already_formatted_date(transformed['appointment_info']['profile']['target_move_date'])
    
    print(f"\nValidation:")
    print(f"  Phone: {'✅' if phone_ok else '❌'}")
    print(f"  DateTime: {'✅' if datetime_ok else '❌'}")
    print(f"  Move Date: {'✅' if date_ok else '❌'}")
    
    if phone_ok and datetime_ok and date_ok:
        print("\n🎉 All transformations successful!")
        return True
    else:
        print("\n⚠️  Some transformations failed")
        return False


def main():
    """Run all transformation tests."""
    print("🔧 Automatic Input Transformation Test Suite")
    print("=" * 60)
    
    tests = [
        test_phone_transformation,
        test_datetime_transformation,
        test_move_date_transformation,
        test_complete_transformation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            result = test_func()
            if result is not False:  # None or True counts as pass
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All automatic transformation tests passed!")
        print("\nThe enhanced OpenAI client will now automatically transform user input")
        print("behind the scenes without mentioning formatting to users.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
