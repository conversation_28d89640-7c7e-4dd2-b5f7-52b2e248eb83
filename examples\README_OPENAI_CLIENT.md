# OpenAI MCP Client with HTTP Transport

This document describes the `client_openai.py` that uses HTTP transport for connecting to MCP servers and integrates with OpenAI's GPT models for intelligent tool usage.

## Overview

The OpenAI MCP client mirrors the functionality of the Claude client (`client_claude.py`) but uses OpenAI's API instead of Anthropic's Claude API. It connects to MCP servers running as HTTP services and provides an interactive chat interface where OpenAI's GPT models can discover and use available tools.

## Key Features

### 1. OpenAI Integration
- **Model**: Uses GPT-4o by default (can be configured to use other models)
- **Function Calling**: Leverages OpenAI's native function calling capabilities
- **Async Support**: Full async/await pattern implementation
- **Error Handling**: Comprehensive error handling for API calls

### 2. MCP Compatibility
- **HTTP Transport**: Uses streamable HTTP transport for MCP communication
- **Tool Discovery**: Automatically discovers and formats tools for OpenAI
- **Session Management**: Handles MCP session lifecycle and cleanup
- **Result Processing**: Converts MCP tool results for OpenAI consumption

### 3. User Experience
- **Interactive Chat**: Command-line chat interface
- **Conversation History**: Maintains context across interactions
- **Progress Indicators**: Shows when the AI is thinking or calling tools
- **Error Recovery**: Graceful handling of connection and API errors

## Installation

### Prerequisites
- Python 3.8+
- OpenAI API key
- Running MCP server with HTTP transport

### Dependencies
```bash
# Using pip
pip install mcp openai python-dotenv

# Using bun (if preferred)
bun install mcp openai python-dotenv
```

### Environment Setup
Create a `.env` file in the examples directory or set environment variables:

```bash
# .env file
OPENAI_API_KEY=your_openai_api_key_here

# Or export directly
export OPENAI_API_KEY=your_openai_api_key_here
```

## Usage

### Basic Usage (Default Server)
```bash
python client_openai.py
```
This connects to `http://localhost:8000/mcp` by default.

### Custom Server URL
```bash
# Local server with custom port
python client_openai.py http://localhost:8000/mcp

# Remote server
python client_openai.py https://your-server.com/mcp
```

### Interactive Commands
Once connected, you can use these commands:
- Type any question or request to interact with OpenAI
- `clear` - Clear conversation history
- `quit`, `exit`, or `q` - Exit the client

## Architecture

### Class Structure
```python
class MCPClient:
    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.openai = AsyncOpenAI()
        self.conversation_history: List[Dict[str, Any]] = []
```

### Key Methods
- `connect(server_url)` - Establish MCP connection
- `disconnect()` - Clean up resources
- `query(query)` - Process user input with OpenAI and tools
- `chat()` - Interactive chat loop
- `clear_conversation_history()` - Reset conversation context

### Tool Integration Flow
1. **Discovery**: List available tools from MCP server
2. **Formatting**: Convert MCP tool schemas to OpenAI function format
3. **Execution**: OpenAI decides when to call tools
4. **Processing**: Execute tools via MCP and return results
5. **Response**: OpenAI processes tool results and responds

## Differences from Claude Client

### API Format
- **Messages**: Uses OpenAI's message format instead of Anthropic's
- **Tools**: Converts to OpenAI's function calling format
- **Responses**: Handles OpenAI's response structure

### Function Calling
- **OpenAI Format**: Uses `tools` parameter with `function` type
- **Tool Results**: Uses `tool` role for tool results instead of `user` role
- **Multiple Calls**: Supports parallel function calling

### Model Configuration
- **Default Model**: GPT-4o (vs Claude-3.5-sonnet for Claude client)
- **Parameters**: Uses OpenAI-specific parameters (max_tokens, etc.)

## Error Handling

The client includes comprehensive error handling for:
- **Connection Errors**: MCP server unavailable or unreachable
- **API Errors**: OpenAI API issues (rate limits, invalid keys, etc.)
- **Tool Errors**: MCP tool execution failures
- **Network Errors**: Timeout and connectivity issues

## Troubleshooting

### Common Issues

1. **"Connection timeout"**
   - Ensure MCP server is running
   - Check server URL is correct
   - Verify network connectivity

2. **"OpenAI API Error"**
   - Verify OPENAI_API_KEY is set correctly
   - Check API key has sufficient credits
   - Ensure API key has required permissions

3. **"Tool execution failed"**
   - Check MCP server logs for errors
   - Verify tool parameters are correct
   - Ensure MCP server supports the requested tool

### Debug Tips
- Enable verbose logging by modifying the client
- Check MCP server health endpoint
- Test OpenAI API key with a simple request
- Verify tool schemas are valid JSON

## Comparison with Claude Client

| Feature | OpenAI Client | Claude Client |
|---------|---------------|---------------|
| Model | GPT-4o | Claude-3.5-sonnet |
| API Provider | OpenAI | Anthropic |
| Function Calling | Native OpenAI format | Anthropic tool format |
| Message Format | OpenAI messages | Anthropic MessageParam |
| Tool Results | `tool` role | `user` role with tool_result |
| Async Support | ✅ | ✅ |
| HTTP Transport | ✅ | ✅ |
| Error Handling | ✅ | ✅ |

## Contributing

When modifying the OpenAI client:
1. Maintain compatibility with the MCP protocol
2. Follow the same patterns as the Claude client
3. Update documentation for any new features
4. Test with various MCP servers and tool configurations
5. Ensure proper error handling and resource cleanup
