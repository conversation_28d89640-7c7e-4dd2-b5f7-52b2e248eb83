#!/usr/bin/env python3
"""
Script to inspect the actual tool schema provided by the MCP server
"""

import asyncio
import json
import sys
from contextlib import AsyncExitStack

from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client


async def inspect_tool_schemas():
    """Connect to MCP server and inspect tool schemas"""
    server_url = "http://localhost:8000/mcp"
    
    async with AsyncExitStack() as stack:
        try:
            # Connect to MCP server
            print(f"Connecting to MCP server at {server_url}...")
            http_transport = await stack.enter_async_context(streamablehttp_client(server_url))
            read_stream, write_stream, session_id = http_transport
            
            # Create MCP session
            session = await stack.enter_async_context(ClientSession(read_stream, write_stream))
            
            # Initialize the session
            await session.initialize()
            
            # List available tools
            print("Discovering available tools...")
            tools_data = await session.list_tools()
            tools = tools_data.tools
            
            print(f"\nFound {len(tools)} tools:")
            for i, tool in enumerate(tools, 1):
                print(f"\n{'='*60}")
                print(f"Tool {i}: {tool.name}")
                print(f"{'='*60}")
                print(f"Description: {tool.description}")
                print(f"\nInput Schema:")
                print(json.dumps(tool.inputSchema, indent=2))
                
                # Focus on schedule_tour tool
                if tool.name == "schedule_tour":
                    print(f"\n{'*'*60}")
                    print("SCHEDULE_TOUR TOOL SCHEMA ANALYSIS")
                    print(f"{'*'*60}")
                    
                    # Show the schema that would be sent to OpenAI
                    openai_tool_param = {
                        "type": "function",
                        "function": {
                            "name": tool.name,
                            "description": tool.description or "",
                            "parameters": tool.inputSchema,
                        },
                    }
                    
                    print("\nOpenAI Tool Parameter Format:")
                    print(json.dumps(openai_tool_param, indent=2))
                    
                    # Show the schema that would be sent to Claude
                    claude_tool_param = {
                        "name": tool.name,
                        "description": tool.description or "",
                        "input_schema": tool.inputSchema,
                    }
                    
                    print("\nClaude Tool Parameter Format:")
                    print(json.dumps(claude_tool_param, indent=2))
                    
        except Exception as e:
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(inspect_tool_schemas())
