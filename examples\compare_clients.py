#!/usr/bin/env python3
"""
Comparison script for <PERSON> and OpenAI MCP Clients

This script compares the structure and functionality of both clients
to ensure they maintain similar interfaces and capabilities.
"""

import sys
import os
from unittest.mock import Mock, patch

# Add the examples directory to the path
sys.path.insert(0, os.path.dirname(__file__))

def compare_client_interfaces():
    """Compare the interfaces of both clients"""
    print("🔍 Comparing Client Interfaces")
    print("=" * 50)
    
    try:
        import client_claude
        import client_openai
        
        # Mock external dependencies for both clients
        with patch('client_claude.Anthropic') as mock_anthropic, \
             patch('client_openai.AsyncOpenAI') as mock_openai:
            
            mock_anthropic.return_value = Mock()
            mock_openai.return_value = Mock()
            
            claude_client = client_claude.MCPClient()
            openai_client = client_openai.MCPClient()
            
            # Compare methods
            claude_methods = set(dir(claude_client))
            openai_methods = set(dir(openai_client))
            
            # Filter to public methods only
            claude_public = {m for m in claude_methods if not m.startswith('_')}
            openai_public = {m for m in openai_methods if not m.startswith('_')}
            
            print(f"📋 Claude client methods: {sorted(claude_public)}")
            print(f"📋 OpenAI client methods: {sorted(openai_public)}")
            
            # Check for common methods
            common_methods = claude_public & openai_public
            claude_only = claude_public - openai_public
            openai_only = openai_public - claude_public
            
            print(f"\n✅ Common methods ({len(common_methods)}): {sorted(common_methods)}")
            
            if claude_only:
                print(f"🔵 Claude-only methods ({len(claude_only)}): {sorted(claude_only)}")
            
            if openai_only:
                print(f"🟠 OpenAI-only methods ({len(openai_only)}): {sorted(openai_only)}")
            
            # Check core methods are present in both
            core_methods = {'connect', 'disconnect', 'query', 'chat', 'clear_conversation_history'}
            missing_claude = core_methods - claude_public
            missing_openai = core_methods - openai_public
            
            if missing_claude:
                print(f"❌ Claude client missing core methods: {missing_claude}")
                return False
            
            if missing_openai:
                print(f"❌ OpenAI client missing core methods: {missing_openai}")
                return False
            
            print("✅ Both clients have all core methods")
            return True
            
    except Exception as e:
        print(f"❌ Comparison failed: {e}")
        return False

def compare_documentation():
    """Compare documentation quality between clients"""
    print("\n📚 Comparing Documentation")
    print("=" * 50)
    
    try:
        import client_claude
        import client_openai
        
        # Compare module docstrings
        claude_doc = client_claude.__doc__ or ""
        openai_doc = client_openai.__doc__ or ""
        
        print(f"📄 Claude module docstring length: {len(claude_doc)} characters")
        print(f"📄 OpenAI module docstring length: {len(openai_doc)} characters")
        
        # Check for key documentation elements
        claude_has_setup = "Setup Instructions" in claude_doc
        openai_has_setup = "Setup Instructions" in openai_doc
        
        claude_has_examples = "Example Usage" in claude_doc
        openai_has_examples = "Example Usage" in openai_doc
        
        print(f"✅ Claude has setup instructions: {claude_has_setup}")
        print(f"✅ OpenAI has setup instructions: {openai_has_setup}")
        print(f"✅ Claude has examples: {claude_has_examples}")
        print(f"✅ OpenAI has examples: {openai_has_examples}")
        
        # Both should have comprehensive documentation
        if not (claude_has_setup and openai_has_setup and claude_has_examples and openai_has_examples):
            print("⚠️  Documentation could be improved")
            return False
        
        print("✅ Both clients have comprehensive documentation")
        return True
        
    except Exception as e:
        print(f"❌ Documentation comparison failed: {e}")
        return False

def compare_dependencies():
    """Compare the dependencies of both clients"""
    print("\n📦 Comparing Dependencies")
    print("=" * 50)
    
    try:
        import client_claude
        import client_openai
        
        # Get imports from both modules
        claude_imports = []
        openai_imports = []
        
        # Read the source files to analyze imports
        with open('examples/client_claude.py', 'r', encoding='utf-8') as f:
            claude_source = f.read()

        with open('examples/client_openai.py', 'r', encoding='utf-8') as f:
            openai_source = f.read()
        
        # Extract key dependencies
        claude_deps = set()
        openai_deps = set()
        
        if 'anthropic' in claude_source:
            claude_deps.add('anthropic')
        if 'openai' in openai_source:
            openai_deps.add('openai')
        if 'mcp' in claude_source:
            claude_deps.add('mcp')
        if 'mcp' in openai_source:
            openai_deps.add('mcp')
        if 'python-dotenv' in claude_source or 'dotenv' in claude_source:
            claude_deps.add('python-dotenv')
        if 'python-dotenv' in openai_source or 'dotenv' in openai_source:
            openai_deps.add('python-dotenv')
        
        print(f"🔵 Claude dependencies: {sorted(claude_deps)}")
        print(f"🟠 OpenAI dependencies: {sorted(openai_deps)}")
        
        # Check common dependencies
        common_deps = claude_deps & openai_deps
        print(f"✅ Common dependencies: {sorted(common_deps)}")
        
        # Both should use MCP and dotenv
        required_common = {'mcp', 'python-dotenv'}
        if not required_common.issubset(common_deps):
            print(f"❌ Missing required common dependencies: {required_common - common_deps}")
            return False
        
        print("✅ Both clients have required common dependencies")
        return True
        
    except Exception as e:
        print(f"❌ Dependency comparison failed: {e}")
        return False

def main():
    """Run all comparisons"""
    print("🔄 Comparing Claude and OpenAI MCP Clients")
    print("=" * 60)
    
    tests = [
        ("Interface Comparison", compare_client_interfaces),
        ("Documentation Comparison", compare_documentation),
        ("Dependencies Comparison", compare_dependencies),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"\n✅ {test_name} PASSED")
            else:
                print(f"\n❌ {test_name} FAILED")
        except Exception as e:
            print(f"\n❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Comparison Results: {passed}/{total} comparisons passed")
    
    if passed == total:
        print("🎉 Both clients are well-aligned and ready to use!")
        print("\n📋 Summary:")
        print("- Both clients provide the same core functionality")
        print("- Both have comprehensive documentation")
        print("- Both use the same MCP transport layer")
        print("- The main difference is the AI provider (Claude vs OpenAI)")
    else:
        print("⚠️  Some differences found. Review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
