# Flow Configuration System Requirements
# These packages enhance the datetime parsing capabilities of the flow system
# The system works without them but provides better natural language parsing with them

# Core requirements (always needed)
pyyaml>=6.0

# Enhanced datetime parsing (optional but recommended)
pytz>=2023.3
python-dateutil>=2.8.0

# Note: If these optional packages are not available, the system will fall back
# to basic datetime parsing and show a friendly informational message.
