#!/usr/bin/env python3
"""
Test script to verify the tool execution fix in the OpenAI MCP Client

This script specifically tests that tools are executed properly without
duplication and that the conversation flow is correct.
"""

import sys
import os
import asyncio
import json
from unittest.mock import Mock, patch, AsyncMock, MagicMock

# Add the examples directory to the path
sys.path.insert(0, os.path.dirname(__file__))

async def test_tool_execution_flow():
    """Test that tools are executed once and results are properly handled"""
    try:
        import client_openai
        
        # Mock external dependencies
        with patch('client_openai.AsyncOpenAI') as mock_openai, \
             patch('client_openai.streamablehttp_client') as mock_http, \
             patch('client_openai.ClientSession') as mock_session:
            
            # Setup OpenAI mock
            mock_openai_instance = AsyncMock()
            mock_openai.return_value = mock_openai_instance
            
            # Setup MCP session mock
            mock_session_instance = AsyncMock()
            mock_session.return_value = mock_session_instance
            
            # Mock tool discovery
            mock_tool = Mock()
            mock_tool.name = "schedule_tour"
            mock_tool.description = "Schedule a tour for the given property and time slots."
            mock_tool.inputSchema = {
                "type": "object",
                "properties": {
                    "request": {
                        "type": "object",
                        "properties": {
                            "appointment_info": {"type": "object"}
                        }
                    }
                }
            }
            
            mock_tools_data = Mock()
            mock_tools_data.tools = [mock_tool]
            mock_session_instance.list_tools.return_value = mock_tools_data
            
            # Mock tool execution result
            mock_tool_result = Mock()
            mock_tool_result.content = [Mock()]
            mock_tool_result.content[0].text = '{"status": "success", "appointment_id": 12345}'
            mock_session_instance.call_tool.return_value = mock_tool_result
            
            # Mock OpenAI responses
            # First response: OpenAI decides to call a tool
            mock_tool_call = Mock()
            mock_tool_call.id = "call_123"
            mock_tool_call.function = Mock()
            mock_tool_call.function.name = "schedule_tour"
            mock_tool_call.function.arguments = '{"request": {"appointment_info": {"property_id": 123}}}'
            
            mock_first_message = Mock()
            mock_first_message.content = "I'll help you schedule a tour."
            mock_first_message.tool_calls = [mock_tool_call]
            
            mock_first_choice = Mock()
            mock_first_choice.message = mock_first_message
            
            mock_first_response = Mock()
            mock_first_response.choices = [mock_first_choice]
            
            # Second response: OpenAI responds to tool results
            mock_second_message = Mock()
            mock_second_message.content = "Great! I've successfully scheduled your tour. Your appointment ID is 12345."
            mock_second_message.tool_calls = None
            
            mock_second_choice = Mock()
            mock_second_choice.message = mock_second_message
            
            mock_second_response = Mock()
            mock_second_response.choices = [mock_second_choice]
            
            # Configure the mock to return different responses for each call
            mock_openai_instance.chat.completions.create.side_effect = [
                mock_first_response,  # Initial response with tool call
                mock_second_response  # Follow-up response after tool execution
            ]
            
            # Setup HTTP transport mock
            mock_http.return_value = (AsyncMock(), AsyncMock(), "session_123")
            
            # Create and test the client
            client = client_openai.MCPClient()
            client.session = mock_session_instance  # Bypass connection for testing
            
            # Test the query method
            result = await client.query("Schedule a tour for property 123")
            
            # Verify tool was called exactly once
            assert mock_session_instance.call_tool.call_count == 1, f"Tool should be called exactly once, but was called {mock_session_instance.call_tool.call_count} times"
            
            # Verify tool was called with correct arguments
            call_args = mock_session_instance.call_tool.call_args
            assert call_args[0][0] == "schedule_tour", "Tool name should be schedule_tour"
            assert call_args[0][1]["request"]["appointment_info"]["property_id"] == 123, "Tool should be called with correct arguments"
            
            # Verify OpenAI was called twice (initial + follow-up)
            assert mock_openai_instance.chat.completions.create.call_count == 2, f"OpenAI should be called twice, but was called {mock_openai_instance.chat.completions.create.call_count} times"
            
            # Verify the result contains both the tool execution info and the final response
            assert "Calling tool schedule_tour" in result, "Result should contain tool execution info"
            assert "successfully scheduled your tour" in result, "Result should contain OpenAI's final response"
            assert "12345" in result, "Result should contain the appointment ID"
            
            # Verify conversation history is properly maintained
            assert len(client.conversation_history) == 4, f"Conversation history should have 4 messages, but has {len(client.conversation_history)}"
            
            # Check conversation history structure
            history = client.conversation_history
            assert history[0]["role"] == "user", "First message should be user"
            assert history[1]["role"] == "assistant", "Second message should be assistant with tool calls"
            assert "tool_calls" in history[1], "Assistant message should contain tool calls"
            assert history[2]["role"] == "tool", "Third message should be tool result"
            assert history[3]["role"] == "assistant", "Fourth message should be assistant response"
            
            print("✅ Tool execution flow test passed")
            return True
            
    except Exception as e:
        print(f"❌ Tool execution flow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_tool_execution_error_handling():
    """Test that tool execution errors are handled gracefully"""
    try:
        import client_openai
        
        # Mock external dependencies
        with patch('client_openai.AsyncOpenAI') as mock_openai, \
             patch('client_openai.streamablehttp_client') as mock_http, \
             patch('client_openai.ClientSession') as mock_session:
            
            # Setup mocks
            mock_openai_instance = AsyncMock()
            mock_openai.return_value = mock_openai_instance
            
            mock_session_instance = AsyncMock()
            mock_session.return_value = mock_session_instance
            
            # Mock tool discovery
            mock_tool = Mock()
            mock_tool.name = "schedule_tour"
            mock_tool.description = "Schedule a tour"
            mock_tool.inputSchema = {"type": "object"}
            
            mock_tools_data = Mock()
            mock_tools_data.tools = [mock_tool]
            mock_session_instance.list_tools.return_value = mock_tools_data
            
            # Mock tool execution to raise an error
            mock_session_instance.call_tool.side_effect = Exception("Tool execution failed")
            
            # Mock OpenAI response with tool call
            mock_tool_call = Mock()
            mock_tool_call.id = "call_123"
            mock_tool_call.function = Mock()
            mock_tool_call.function.name = "schedule_tour"
            mock_tool_call.function.arguments = '{"test": "data"}'
            
            mock_message = Mock()
            mock_message.content = "I'll try to schedule a tour."
            mock_message.tool_calls = [mock_tool_call]
            
            mock_choice = Mock()
            mock_choice.message = mock_message
            
            mock_response = Mock()
            mock_response.choices = [mock_choice]
            
            # Mock follow-up response
            mock_follow_up_message = Mock()
            mock_follow_up_message.content = "I encountered an error while scheduling the tour."
            mock_follow_up_message.tool_calls = None
            
            mock_follow_up_choice = Mock()
            mock_follow_up_choice.message = mock_follow_up_message
            
            mock_follow_up_response = Mock()
            mock_follow_up_response.choices = [mock_follow_up_choice]
            
            mock_openai_instance.chat.completions.create.side_effect = [
                mock_response,
                mock_follow_up_response
            ]
            
            # Setup HTTP transport mock
            mock_http.return_value = (AsyncMock(), AsyncMock(), "session_123")
            
            # Create and test the client
            client = client_openai.MCPClient()
            client.session = mock_session_instance
            
            # Test the query method with error
            result = await client.query("Schedule a tour")
            
            # Verify error was handled gracefully
            assert "Error executing tool schedule_tour" in result or "error while scheduling" in result, "Result should contain error information"
            
            # Verify conversation history includes error message
            history = client.conversation_history
            tool_message = next((msg for msg in history if msg.get("role") == "tool"), None)
            assert tool_message is not None, "Should have tool result message even on error"
            assert "Error executing tool" in tool_message["content"], "Tool result should contain error message"
            
            print("✅ Tool execution error handling test passed")
            return True
            
    except Exception as e:
        print(f"❌ Tool execution error handling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tool execution tests"""
    print("🧪 Testing OpenAI MCP Client Tool Execution Fix")
    print("=" * 60)
    
    tests = [
        ("Tool Execution Flow", test_tool_execution_flow),
        ("Tool Execution Error Handling", test_tool_execution_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tool execution tests passed! The fix is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. The fix may need additional work.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
