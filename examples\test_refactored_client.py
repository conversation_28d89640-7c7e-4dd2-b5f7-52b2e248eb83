#!/usr/bin/env python3
"""
Test script for the refactored tool-agnostic client_openai.py

This script tests that the refactored client:
1. Works with multiple tools without hardcoded logic
2. Dynamically discovers and handles all available MCP tools
3. Applies transformations generically based on flow configurations
4. Maintains the same functionality as before
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the examples directory to the path so we can import the client
examples_dir = Path(__file__).parent
sys.path.insert(0, str(examples_dir))

from client_openai import MCPClient

async def test_tool_discovery():
    """Test that the client can discover tools dynamically."""
    print("🧪 Testing tool discovery...")
    
    client = MCPClient()
    
    try:
        # Connect to the MCP server (assuming it's running)
        await client.connect("http://localhost:8000/mcp")
        
        # The connection process should have discovered tools
        print(f"✅ Discovered {len(client.available_tools)} tools: {client.available_tools}")
        
        # Verify that the client doesn't have any hardcoded tool references
        assert len(client.available_tools) > 0, "Should discover at least one tool"
        
        return True
        
    except Exception as e:
        print(f"❌ Tool discovery test failed: {e}")
        return False
    finally:
        await client.disconnect()

async def test_flow_configuration_loading():
    """Test that flow configurations are loaded dynamically for any tool."""
    print("\n🧪 Testing flow configuration loading...")
    
    client = MCPClient()
    
    try:
        # Test that flow configurations can be loaded for different tools
        from flows.flow_loader import flow_loader
        
        # Test with known tools that have flow configurations
        test_tools = ["schedule_tour", "get_property_available_times", "pricing_and_availability"]
        
        for tool_name in test_tools:
            flow_config = flow_loader.load_flow_configuration(tool_name)
            if flow_config:
                print(f"✅ Flow configuration loaded for {tool_name}")
                
                # Verify the configuration has the expected structure
                assert hasattr(flow_config, 'tool_name'), f"Flow config for {tool_name} missing tool_name"
                assert hasattr(flow_config, 'input_transformations'), f"Flow config for {tool_name} missing input_transformations"
                
                # Test enhanced system prompt generation
                enhanced_prompt = flow_loader.get_enhanced_system_prompt(tool_name)
                assert enhanced_prompt, f"Enhanced prompt should be generated for {tool_name}"
                print(f"   Enhanced prompt generated: {len(enhanced_prompt)} characters")
            else:
                print(f"⚠️  No flow configuration found for {tool_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Flow configuration test failed: {e}")
        return False

async def test_input_transformation_system():
    """Test that the generic input transformation system works."""
    print("\n🧪 Testing generic input transformation system...")
    
    client = MCPClient()
    
    try:
        # Test the generic transformation methods
        from flows.flow_loader import flow_loader
        
        # Test phone number transformation
        test_phone = "************"
        transformed_phone = flow_loader.input_transformer.transform_input("phone_number", test_phone)
        print(f"✅ Phone transformation: '{test_phone}' → '{transformed_phone}'")
        assert transformed_phone == "(*************", f"Phone transformation failed: {transformed_phone}"
        
        # Test datetime transformation
        test_datetime = "tomorrow at 2 PM"
        try:
            transformed_datetime = flow_loader.input_transformer.transform_input("datetime", test_datetime)
            print(f"✅ Datetime transformation: '{test_datetime}' → '{transformed_datetime}'")
            assert "T14:" in transformed_datetime, f"Datetime transformation failed: {transformed_datetime}"
        except Exception as e:
            print(f"⚠️  Datetime transformation test skipped (missing dependencies): {e}")
        
        # Test the generic nested data transformation
        test_data = {
            "profile": {
                "phone_number": "5551234567",
                "name": "Test User"
            },
            "requested_times": [
                {"start_time": "tomorrow at 3 PM"}
            ]
        }
        
        # Load a flow configuration to test with
        flow_config = flow_loader.load_flow_configuration("schedule_tour")
        if flow_config:
            transformed_data = client._transform_nested_data(
                test_data, 
                flow_config.input_transformations, 
                flow_loader.input_transformer
            )
            print(f"✅ Nested data transformation completed")
            
            # Verify phone was transformed
            if "phone_number" in transformed_data.get("profile", {}):
                transformed_phone = transformed_data["profile"]["phone_number"]
                assert "(" in transformed_phone and ")" in transformed_phone, f"Phone not transformed: {transformed_phone}"
        
        return True
        
    except Exception as e:
        print(f"❌ Input transformation test failed: {e}")
        return False

async def test_tool_agnostic_behavior():
    """Test that the client behaves the same way for any tool."""
    print("\n🧪 Testing tool-agnostic behavior...")
    
    client = MCPClient()
    
    try:
        # Test that the client can handle any tool name without special cases
        test_tools = ["schedule_tour", "get_property_available_times", "pricing_and_availability", "unknown_tool"]
        
        for tool_name in test_tools:
            # Test input transformation application
            test_args = {"phone_number": "************", "other_field": "test"}
            
            try:
                transformed_args = client._apply_input_transformations(tool_name, test_args)
                print(f"✅ Input transformation applied for {tool_name}")
                
                # The method should always return a dict, even for unknown tools
                assert isinstance(transformed_args, dict), f"Should return dict for {tool_name}"
                
            except Exception as e:
                print(f"❌ Input transformation failed for {tool_name}: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Tool-agnostic behavior test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Testing refactored tool-agnostic client_openai.py")
    print("=" * 60)
    
    tests = [
        test_flow_configuration_loading,
        test_input_transformation_system,
        test_tool_agnostic_behavior,
        # test_tool_discovery,  # Commented out as it requires a running MCP server
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {i+1}. {test.__name__}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The client is successfully tool-agnostic.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
