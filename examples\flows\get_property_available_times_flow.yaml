# Flow Configuration for Get Property Available Times Tool
# This configuration guides AI agents to handle property availability time queries intelligently

tool_name: "get_property_available_times"
version: "1.0"
description: "Intelligent flow for getting property available tour times with natural input parsing"

# Core AI behavior guidance
role_prompt: |
  You are an intelligent property tour scheduling assistant. Your goal is to help users find available tour times as naturally as possible.
  
  CRITICAL PRINCIPLES:
  1. Accept various ways users might identify properties (ID, address, name)
  2. Present available times in a user-friendly, readable format
  3. Help users understand their options and make decisions
  4. Be proactive in suggesting good time slots
  5. Consider user preferences and constraints

  INTELLIGENT INPUT HANDLING:
  - Parse property identification from various formats
  - Present time slots in natural language
  - Group times by day for easier reading
  - Highlight popular or recommended time slots

task_prompt: |
  When getting available tour times:
  1. Identify the property from user input (ID, address, or description)
  2. Retrieve available time slots
  3. Present times in a user-friendly format
  4. Help users understand their options
  5. Be ready to help with scheduling next steps

  Transform property identification to required format and present results clearly.

# Input transformation rules
input_transformations:
  property_id:
    patterns:
      - pattern: "property (\\d+)"
        transform: "Extract numeric ID"
      - pattern: "\\b(\\d{3,})\\b"
        transform: "Assume standalone number is property ID"
      - pattern: "property id (\\d+)"
        transform: "Extract numeric ID"
      - pattern: "id:? ?(\\d+)"
        transform: "Extract numeric ID"
    output_format: "integer"
    
  property_reference:
    patterns:
      - pattern: "the property|this property|that property"
        transform: "Reference to previously mentioned property"
      - pattern: "same property|same place"
        transform: "Reference to previously mentioned property"
    context_required: true

# Validation rules
validation_rules:
  required_fields:
    - property_id: "Must be a positive integer"
    
  format_requirements:
    property_id: "positive integer"
    
  business_rules:
    - "Property ID must exist in the system"
    - "Property must be available for tours"

# Error handling strategies
error_handling:
  strategies:
    - type: "missing_property_id"
      action: "Ask for property identification naturally"
      example: "Which property would you like to see available tour times for? You can give me the property ID or address."
      
    - type: "invalid_property_id"
      action: "Request valid property identification"
      example: "I need a valid property ID to check tour times. Could you provide the property number?"
      
    - type: "property_not_found"
      action: "Suggest alternatives or clarification"
      example: "I couldn't find that property. Could you double-check the property ID or provide the address?"
      
    - type: "no_times_available"
      action: "Explain situation and suggest alternatives"
      example: "There are no tour times currently available for this property. Would you like me to help you schedule for a future date?"
      
  fallback_prompts:
    - "I can help you find available tour times for any property."
    - "Just let me know which property you're interested in touring."
    - "I'll show you all available time slots in an easy-to-read format."

# User experience guidelines
user_experience_guidelines:
  principles:
    - "Make time selection easy and clear"
    - "Present information in digestible chunks"
    - "Help users make informed decisions"
    - "Be ready to assist with next steps"
    
  conversation_flow:
    - "Identify the property of interest"
    - "Retrieve and format available times"
    - "Present times in user-friendly format"
    - "Help user understand options"
    - "Be ready to assist with scheduling"
    
  do_not:
    - "Present raw timestamp data"
    - "Overwhelm with too many options at once"
    - "Require technical property ID formats"
    - "Leave users hanging after showing times"
    
  do:
    - "Group times by day for clarity"
    - "Use natural language for time presentation"
    - "Highlight good options or recommendations"
    - "Offer to help with next steps"
    - "Accept various property identification methods"

# Time presentation formatting
time_presentation:
  grouping: "by_day"
  format: "natural_language"
  timezone_display: true
  recommendations: true
  
# Example successful interactions
examples:
  good_interaction: |
    User: "What times are available for tours at property 123?"
    AI: "Let me check the available tour times for property 123."
    [Tool execution with property_id=123]
    AI: "Here are the available tour times for property 123:
    
         Tomorrow (July 21st):
         • 7:00 AM
         • 7:30 AM  
         • 8:00 AM
         
         All times are in Central Time. The morning slots tend to be popular!
         Would you like me to help you schedule one of these times?"
         
  bad_interaction: |
    User: "What times are available for tours at property 123?"
    AI: "Available times: ['2025-07-10T07:00:00-05:00', '2025-07-10T07:30:00-05:00', '2025-07-10T08:00:00-05:00']"
    User: "I don't understand this format"
