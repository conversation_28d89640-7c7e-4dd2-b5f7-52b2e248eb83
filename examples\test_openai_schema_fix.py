#!/usr/bin/env python3
"""
Test script to verify that OpenAI generates correct parameters with the resolved schema
"""

import asyncio
import json
import sys
from unittest.mock import Mock, patch, AsyncMock

# Import the client
sys.path.insert(0, '.')
import client_openai


async def test_openai_parameter_generation():
    """Test that OpenAI generates correct parameters with resolved schema"""
    try:
        # Mock external dependencies
        with patch('client_openai.AsyncOpenAI') as mock_openai, \
             patch('client_openai.streamablehttp_client') as mock_http, \
             patch('client_openai.ClientSession') as mock_session:
            
            # Setup OpenAI mock
            mock_openai_instance = AsyncMock()
            mock_openai.return_value = mock_openai_instance
            
            # Setup MCP session mock
            mock_session_instance = AsyncMock()
            mock_session.return_value = mock_session_instance
            
            # Create the actual schedule_tour tool schema (with $defs)
            schedule_tour_schema = {
                "$defs": {
                    "AppointmentInfo": {
                        "properties": {
                            "property_id": {
                                "default": 1,
                                "exclusiveMinimum": 0,
                                "title": "Property Id",
                                "type": "integer"
                            },
                            "requested_times": {
                                "default": [],
                                "items": {
                                    "$ref": "#/$defs/RequestedTime"
                                },
                                "minItems": 1,
                                "title": "Requested Times",
                                "type": "array"
                            },
                            "profile": {
                                "$ref": "#/$defs/Profile"
                            },
                            "marketing_source": {
                                "$ref": "#/$defs/MarketingSource"
                            },
                            "preferences": {
                                "$ref": "#/$defs/Preferences"
                            }
                        },
                        "required": ["profile", "marketing_source", "preferences"],
                        "title": "AppointmentInfo",
                        "type": "object"
                    },
                    "RequestedTime": {
                        "properties": {
                            "start_time": {
                                "default": "",
                                "description": "Start time in ISO format with timezone",
                                "pattern": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:?\\d{2}$",
                                "title": "Start Time",
                                "type": "string"
                            }
                        },
                        "title": "RequestedTime",
                        "type": "object"
                    },
                    "Profile": {
                        "properties": {
                            "first_name": {
                                "minLength": 1,
                                "title": "First Name",
                                "type": "string"
                            },
                            "last_name": {
                                "minLength": 1,
                                "title": "Last Name",
                                "type": "string"
                            },
                            "email": {
                                "format": "email",
                                "title": "Email",
                                "type": "string"
                            },
                            "phone_number": {
                                "default": "",
                                "pattern": "^\\(\\d{3}\\) \\d{3}-\\d{4}$",
                                "title": "Phone Number",
                                "type": "string"
                            },
                            "target_move_date": {
                                "default": "",
                                "pattern": "^\\d{4}-\\d{2}-\\d{2}$",
                                "title": "Target Move Date",
                                "type": "string"
                            },
                            "pets": {
                                "$ref": "#/$defs/Pets"
                            }
                        },
                        "required": ["first_name", "last_name", "email", "pets"],
                        "title": "Profile",
                        "type": "object"
                    },
                    "Pets": {
                        "properties": {
                            "cats": {
                                "title": "Cats",
                                "type": "boolean"
                            },
                            "large_dogs": {
                                "title": "Large Dogs",
                                "type": "boolean"
                            },
                            "none": {
                                "title": "None",
                                "type": "boolean"
                            },
                            "small_dogs": {
                                "title": "Small Dogs",
                                "type": "boolean"
                            }
                        },
                        "required": ["cats", "large_dogs", "none", "small_dogs"],
                        "title": "Pets",
                        "type": "object"
                    },
                    "MarketingSource": {
                        "properties": {
                            "code": {
                                "minLength": 1,
                                "title": "Code",
                                "type": "string"
                            },
                            "domain": {
                                "title": "Domain",
                                "type": "string"
                            },
                            "doorway_application_id": {
                                "default": "",
                                "minLength": 1,
                                "title": "Doorway Application Id",
                                "type": "string"
                            }
                        },
                        "required": ["code", "domain"],
                        "title": "MarketingSource",
                        "type": "object"
                    },
                    "Preferences": {
                        "properties": {
                            "min_price": {
                                "default": 0,
                                "minimum": 0,
                                "title": "Min Price",
                                "type": "integer"
                            },
                            "max_price": {
                                "default": 0,
                                "minimum": 0,
                                "title": "Max Price",
                                "type": "integer"
                            },
                            "bedrooms": {
                                "default": [],
                                "items": {
                                    "type": "integer"
                                },
                                "title": "Bedrooms",
                                "type": "array"
                            },
                            "selected_times": {
                                "default": [],
                                "items": {
                                    "type": "string"
                                },
                                "minItems": 1,
                                "title": "Selected Times",
                                "type": "array"
                            }
                        },
                        "title": "Preferences",
                        "type": "object"
                    },
                    "ScheduleTourRequest": {
                        "properties": {
                            "appointment_info": {
                                "$ref": "#/$defs/AppointmentInfo"
                            }
                        },
                        "required": ["appointment_info"],
                        "title": "ScheduleTourRequest",
                        "type": "object"
                    }
                },
                "properties": {
                    "request": {
                        "$ref": "#/$defs/ScheduleTourRequest",
                        "title": "Request"
                    }
                },
                "required": ["request"],
                "type": "object"
            }
            
            # Mock tool discovery
            mock_tool = Mock()
            mock_tool.name = "schedule_tour"
            mock_tool.description = "Schedule a tour for the given property and time slots."
            mock_tool.inputSchema = schedule_tour_schema
            
            mock_tools_data = Mock()
            mock_tools_data.tools = [mock_tool]
            mock_session_instance.list_tools.return_value = mock_tools_data
            
            # Mock tool execution result
            mock_tool_result = Mock()
            mock_tool_result.content = [Mock()]
            mock_tool_result.content[0].text = '{"status": "success", "appointment_id": 12345}'
            mock_session_instance.call_tool.return_value = mock_tool_result
            
            # Setup HTTP transport mock
            mock_http.return_value = (AsyncMock(), AsyncMock(), "session_123")
            
            # Create and test the client
            client = client_openai.MCPClient()
            client.session = mock_session_instance  # Bypass connection for testing
            
            # Test schema resolution
            print("🧪 Testing schema resolution...")
            resolved_schema = client_openai.resolve_json_schema_refs(schedule_tour_schema)
            
            # Verify the schema was properly resolved
            assert "$defs" not in resolved_schema, "Resolved schema should not contain $defs"
            assert "properties" in resolved_schema, "Resolved schema should have properties"
            assert "request" in resolved_schema["properties"], "Resolved schema should have request property"
            
            request_prop = resolved_schema["properties"]["request"]
            assert "properties" in request_prop, "Request should have properties"
            assert "appointment_info" in request_prop["properties"], "Request should have appointment_info"
            
            appointment_info = request_prop["properties"]["appointment_info"]
            assert "properties" in appointment_info, "AppointmentInfo should have properties"
            
            # Check that all required nested objects are properly resolved
            required_props = ["profile", "marketing_source", "preferences", "requested_times"]
            for prop in required_props:
                assert prop in appointment_info["properties"], f"AppointmentInfo should have {prop}"
                
            # Check that profile has pets properly resolved
            profile = appointment_info["properties"]["profile"]
            assert "properties" in profile, "Profile should have properties"
            assert "pets" in profile["properties"], "Profile should have pets"
            
            pets = profile["properties"]["pets"]
            assert "properties" in pets, "Pets should have properties"
            expected_pet_fields = ["cats", "large_dogs", "none", "small_dogs"]
            for field in expected_pet_fields:
                assert field in pets["properties"], f"Pets should have {field} field"
            
            print("✅ Schema resolution test passed")
            
            # Test that the resolved schema produces better OpenAI tool parameters
            print("🧪 Testing OpenAI tool parameter generation...")
            
            # Simulate what the client does
            tool_param = {
                "type": "function",
                "function": {
                    "name": mock_tool.name,
                    "description": mock_tool.description,
                    "parameters": resolved_schema,
                },
            }
            
            # Verify the tool parameter structure
            assert tool_param["type"] == "function", "Tool param should be function type"
            assert tool_param["function"]["name"] == "schedule_tour", "Tool name should be schedule_tour"
            
            # The key test: verify that the parameters section is fully resolved
            params = tool_param["function"]["parameters"]
            assert "$defs" not in params, "Tool parameters should not contain $defs"
            
            # Verify that nested structures are accessible without references
            request_schema = params["properties"]["request"]
            appointment_info_schema = request_schema["properties"]["appointment_info"]
            profile_schema = appointment_info_schema["properties"]["profile"]
            pets_schema = profile_schema["properties"]["pets"]
            
            # This should work without any $ref resolution needed by OpenAI
            assert pets_schema["properties"]["none"]["type"] == "boolean", "Pets.none should be boolean"
            assert pets_schema["properties"]["cats"]["type"] == "boolean", "Pets.cats should be boolean"
            
            print("✅ OpenAI tool parameter generation test passed")
            print("✅ Schema fix verification completed successfully")
            
            return True
            
    except Exception as e:
        print(f"❌ Schema fix verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the schema fix verification test"""
    print("🧪 Testing OpenAI Schema Fix")
    print("=" * 60)
    
    success = await test_openai_parameter_generation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All schema fix tests passed! OpenAI should now generate correct parameters.")
    else:
        print("⚠️  Schema fix tests failed. OpenAI may still generate incorrect parameters.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
