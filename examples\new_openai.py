"""
OpenAI MCP Client with HTTP Transport

This client connects to MCP servers over HTTP transport, allowing you to chat
with an OpenAI model (via AsyncOpenAI) and leverage MCP tools.

Setup Instructions:
1. Install dependencies:
     pip install mcp openai python-dotenv
2. Set your OpenAI key:
     export OPENAI_API_KEY=your_key_here
3. Start the MCP server:
     uvicorn main:app --host 0.0.0.0 --port 8000
4. Run this client:
     python new_openai.py [server_url]
"""

from __future__ import annotations
import asyncio
import json
import sys
from typing import Optional, List, Any, Dict
from contextlib import AsyncExitStack
from urllib.parse import urlparse

from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client

from openai import AsyncOpenAI
from openai.types.chat import ChatCompletion
from typing import TypedDict

from dotenv import load_dotenv
load_dotenv()  # loads OPENAI_API_KEY, etc.

MODEL = "gpt-4o"


class ChatMessage(TypedDict, total=False):
    role: str
    content: str
    name: Optional[str]


class OpenAIMCPClient:
    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.openai = AsyncOpenAI()
        self.conversation_history: List[ChatMessage] = []

    async def connect(self, server_url: str = "http://localhost:8000/mcp"):
        """Connect to MCP server over HTTP transport."""
        # URL validation
        parsed = urlparse(server_url)
        if not parsed.scheme or not parsed.netloc:
            raise ValueError(f"Invalid server URL: {server_url}")
        if parsed.scheme not in ("http", "https"):
            raise ValueError("URL scheme must be http or https")

        try:
            print(f"→ Establishing HTTP transport to {server_url}…")
            read_stream, write_stream, self.session_id = await asyncio.wait_for(
                self.exit_stack.enter_async_context(streamablehttp_client(server_url)),
                timeout=30.0
            )

            print("→ Creating MCP session…")
            self.session = await self.exit_stack.enter_async_context(
                ClientSession(read_stream, write_stream)
            )

            print("→ Initializing MCP session…")
            await asyncio.wait_for(self.session.initialize(), timeout=10.0)

            tools_info = await self.session.list_tools()
            tool_names = [t.name for t in tools_info.tools]
            print(f"✅ Connected! Session ID = {self.session_id}")
            print("📋 Available tools:", tool_names)

        except asyncio.TimeoutError:
            raise RuntimeError(f"Connection timed out connecting to {server_url}")
        except ConnectionError as e:
            raise RuntimeError(f"Connection failed: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to connect: {e}")

    async def disconnect(self):
        """Gracefully close connection to MCP server."""
        try:
            if self.exit_stack:
                print("→ Disconnecting from MCP server…")
                await self.exit_stack.aclose()
                print("✅ Disconnected successfully")
        except Exception as e:
            print(f"⚠️  Warning during disconnect: {e}")

    def clear_conversation_history(self):
        """Clear the in-memory chat history."""
        self.conversation_history.clear()
        print("🔄 Conversation history cleared")

    async def query(self, user_input: str) -> str:
        """Send a user query to OpenAI (with tools) and return the assistant's response."""
        if not self.session:
            raise RuntimeError("Not connected. Call connect() first.")

        # Append user message
        self.conversation_history.append({"role": "user", "content": user_input})

        # Discover tools
        tool_data = await self.session.list_tools()
        available_tools = [
            {"name": t.name, "description": t.description or "", "input_schema": t.inputSchema}
            for t in tool_data.tools
        ]

        # Agent loop: call model → possibly invoke tools → repeat
        while True:
            completion: ChatCompletion = await self.openai.chat.completions.create(
                model=MODEL,
                messages=self.conversation_history,
                tools=available_tools,
            )
            choice = completion.choices[0]
            msg = choice.message
            assistant_text = msg.content or ""

            # If the model requested any tool calls…
            if msg.tool_calls:
                for tc in msg.tool_calls:
                    name = tc.function.name
                    args = tc.function.arguments or {}
                    print(f"→ Calling tool `{name}` with {args}…")
                    result = await self.session.call_tool(name, args)

                    # Add tool result into the conversation
                    self.conversation_history.append({
                        "role": "tool",
                        "name": name,
                        "content": json.dumps(result),
                    })

                # Loop again so the model can incorporate the tool outputs
                continue

            # No more tool calls: finalize response
            self.conversation_history.append({
                "role": "assistant",
                "content": assistant_text,
            })
            return assistant_text

    async def chat(self):
        """Interactive REPL loop."""
        print("\n🟢 OpenAI MCP Client Ready!")
        print("Type your questions, 'clear' to reset history, or 'quit' to exit.\n")

        while True:
            try:
                user_in = input("> ").strip()
                if not user_in:
                    continue
                if user_in.lower() in ("quit", "exit", "q"):
                    print("👋 Goodbye!")
                    break
                if user_in.lower() == "clear":
                    self.clear_conversation_history()
                    continue

                print("🤔 Thinking…")
                response = await self.query(user_in)
                print("\n" + "=" * 50)
                print(response)
                print("=" * 50 + "\n")

            except KeyboardInterrupt:
                print("\nInterrupted. Type 'quit' to exit.")
            except ConnectionError as e:
                print(f"\n❌ Connection error: {e}")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")

async def main():
    # Parse server URL from argv
    if len(sys.argv) == 1:
        server_url = "http://localhost:8000/mcp"
        print(f"No server URL provided—using default: {server_url}")
    elif len(sys.argv) == 2:
        server_url = sys.argv[1]
    else:
        print("Usage: python new_openai.py [server_url]")
        sys.exit(1)

    client = OpenAIMCPClient()
    try:
        print(f"🔗 Connecting to MCP server at {server_url}…")
        await client.connect(server_url=server_url)
        await client.chat()

    except KeyboardInterrupt:
        print("\nInterrupted by user—shutting down.")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("\nTroubleshooting:")
        print("1. Ensure your MCP server is running.")
        print("2. Verify the URL is correct and uses http/https.")
        print("3. Make sure OPENAI_API_KEY is set in your environment.")
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
