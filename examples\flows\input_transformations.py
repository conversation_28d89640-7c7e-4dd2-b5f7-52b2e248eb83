"""
Input Transformation Utilities for Flow Configuration System

This module provides utilities for intelligently parsing and transforming
user input into the required formats for MCP tools, eliminating the need
for users to provide perfectly formatted input.
"""

import re
from datetime import datetime, timedelta, time
from typing import Optional, Dict, Any, List

try:
    import pytz
    from dateutil import parser as date_parser
    from dateutil.relativedelta import relativedelta
    DATETIME_LIBS_AVAILABLE = True
except ImportError:
    DATETIME_LIBS_AVAILABLE = False
    # Only print warning once, not on every import
    import os
    if not os.environ.get('FLOW_DATETIME_WARNING_SHOWN'):
        print("ℹ️  Note: Advanced datetime parsing libraries not available. Using basic parsing.")
        os.environ['FLOW_DATETIME_WARNING_SHOWN'] = '1'


class InputTransformationError(Exception):
    """Raised when input transformation fails."""
    pass


class DateTimeTransformer:
    """Handles intelligent parsing and transformation of datetime inputs."""
    
    def __init__(self, default_timezone: str = "America/Chicago"):
        if DATETIME_LIBS_AVAILABLE:
            self.default_timezone = pytz.timezone(default_timezone)
            self.now = datetime.now(self.default_timezone)
        else:
            self.default_timezone = None
            self.now = datetime.now()
    
    def transform_natural_datetime(self, user_input: str) -> str:
        """
        Transform natural language datetime input to ISO format with timezone.
        
        Args:
            user_input: Natural language datetime like "tomorrow at 2 PM"
            
        Returns:
            ISO formatted datetime string with timezone
            
        Raises:
            InputTransformationError: If input cannot be parsed
        """
        user_input = user_input.lower().strip()
        
        try:
            
            # Handle "next [weekday]"
            weekday_match = re.search(r'next\s+(monday|tuesday|wednesday|thursday|friday|saturday|sunday)', user_input)
            if weekday_match:
                weekday = weekday_match.group(1)
                base_date = self._get_next_weekday(weekday)
                time_part = self._extract_time_from_input(user_input)
                return self._combine_date_time(base_date.date(), time_part)

            # Handle "tomorrow" with time
            if "tomorrow" in user_input:
                base_date = self.now + timedelta(days=1)
                time_part = self._extract_time_from_input(user_input)
                return self._combine_date_time(base_date.date(), time_part)
            
            # Handle relative time expressions
            if user_input in ["morning", "this morning"]:
                return self._combine_date_time(self.now.date(), "10:00")
            elif user_input in ["afternoon", "this afternoon"]:
                return self._combine_date_time(self.now.date(), "14:00")
            elif user_input in ["evening", "this evening"]:
                return self._combine_date_time(self.now.date(), "18:00")
            
            # Handle time-only inputs like "2 PM", "14:30"
            time_match = re.search(r'(\d{1,2})(?::(\d{2}))?\s*(am|pm)?', user_input)
            if time_match and not re.search(r'\d{4}', user_input):  # No year in input
                hour = int(time_match.group(1))
                minute = int(time_match.group(2)) if time_match.group(2) else 0
                ampm = time_match.group(3)

                if ampm:
                    if ampm == 'pm' and hour != 12:
                        hour += 12
                    elif ampm == 'am' and hour == 12:
                        hour = 0

                time_str = f"{hour:02d}:{minute:02d}"

                # Check if this is combined with "tomorrow"
                if "tomorrow" in user_input:
                    base_date = self.now + timedelta(days=1)
                    return self._combine_date_time(base_date.date(), time_str)
                else:
                    return self._combine_date_time(self.now.date(), time_str)
            
            # Try to parse with dateutil as fallback
            if DATETIME_LIBS_AVAILABLE:
                parsed_dt = date_parser.parse(user_input, default=self.now)
                if parsed_dt.tzinfo is None and self.default_timezone:
                    parsed_dt = self.default_timezone.localize(parsed_dt)
                return parsed_dt.isoformat()
            else:
                # Simple fallback without dateutil
                return self._combine_date_time(self.now.date(), "10:00")
            
        except Exception as e:
            raise InputTransformationError(f"Could not parse datetime '{user_input}': {e}")
    
    def _extract_time_from_input(self, text: str) -> str:
        """Extract time portion from natural language input."""
        # Look for time patterns
        time_patterns = [
            r'(\d{1,2}):(\d{2})\s*(am|pm)',
            r'(\d{1,2})\s*(am|pm)',
            r'(\d{1,2}):(\d{2})',
        ]

        for pattern in time_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                hour = int(match.group(1))
                minute = 0
                ampm = None

                # Handle different group patterns
                if len(match.groups()) >= 2:
                    if match.group(2) and match.group(2).isdigit():
                        minute = int(match.group(2))
                        if len(match.groups()) >= 3:
                            ampm = match.group(3)
                    else:
                        ampm = match.group(2)

                if ampm:
                    ampm = ampm.lower()
                    if ampm == 'pm' and hour != 12:
                        hour += 12
                    elif ampm == 'am' and hour == 12:
                        hour = 0

                return f"{hour:02d}:{minute:02d}"
        
        # Default times for common expressions
        if any(word in text for word in ["morning", "am"]):
            return "10:00"
        elif any(word in text for word in ["afternoon", "pm"]):
            return "14:00"
        elif any(word in text for word in ["evening", "night"]):
            return "18:00"
        
        return "10:00"  # Default fallback
    
    def _get_next_weekday(self, weekday_name: str) -> datetime:
        """Get the next occurrence of a specific weekday."""
        weekdays = {
            'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
            'friday': 4, 'saturday': 5, 'sunday': 6
        }
        
        target_weekday = weekdays[weekday_name.lower()]
        days_ahead = target_weekday - self.now.weekday()
        
        if days_ahead <= 0:  # Target day already happened this week
            days_ahead += 7
        
        return self.now + timedelta(days=days_ahead)
    
    def _combine_date_time(self, date_part, time_str: str) -> str:
        """Combine date and time into ISO format with timezone."""
        hour, minute = map(int, time_str.split(':'))
        dt = datetime.combine(date_part, time(hour=hour, minute=minute))
        if self.default_timezone:
            dt = self.default_timezone.localize(dt)
            return dt.isoformat()
        else:
            # Fallback without timezone
            return dt.strftime("%Y-%m-%dT%H:%M:%S")


class PhoneNumberTransformer:
    """Handles intelligent parsing and transformation of phone number inputs."""
    
    @staticmethod
    def transform_phone_number(user_input: str) -> str:
        """
        Transform various phone number formats to standard (XXX) XXX-XXXX format.
        
        Args:
            user_input: Phone number in various formats
            
        Returns:
            Standardized phone number format
            
        Raises:
            InputTransformationError: If phone number cannot be parsed
        """
        # Remove all non-digit characters
        digits = re.sub(r'\D', '', user_input)
        
        # Handle different digit counts
        if len(digits) == 10:
            # Standard US number without country code
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == '1':
            # US number with country code
            return f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        else:
            raise InputTransformationError(f"Invalid phone number format: {user_input}")


class MoveDateTransformer:
    """Handles intelligent parsing and transformation of move date inputs."""
    
    def __init__(self):
        self.now = datetime.now()
    
    def transform_move_date(self, user_input: str) -> str:
        """
        Transform natural language move date to YYYY-MM-DD format.
        
        Args:
            user_input: Natural language move date like "next month"
            
        Returns:
            Date in YYYY-MM-DD format
            
        Raises:
            InputTransformationError: If date cannot be parsed
        """
        user_input = user_input.lower().strip()
        
        try:
            # Handle relative expressions
            if "next month" in user_input:
                if DATETIME_LIBS_AVAILABLE:
                    next_month = self.now + relativedelta(months=1)
                    return next_month.strftime("%Y-%m-01")  # First day of next month
                else:
                    # Simple fallback - approximate next month as 30 days
                    next_month = self.now + timedelta(days=30)
                    return next_month.strftime("%Y-%m-01")
            
            # Handle "in X months"
            months_match = re.search(r'in\s+(\d+)\s+months?', user_input)
            if months_match:
                months_ahead = int(months_match.group(1))
                if DATETIME_LIBS_AVAILABLE:
                    future_date = self.now + relativedelta(months=months_ahead)
                    return future_date.strftime("%Y-%m-01")
                else:
                    # Simple fallback - approximate months as 30 days
                    future_date = self.now + timedelta(days=30 * months_ahead)
                    return future_date.strftime("%Y-%m-01")
            
            # Handle month names
            month_names = {
                'january': 1, 'february': 2, 'march': 3, 'april': 4,
                'may': 5, 'june': 6, 'july': 7, 'august': 8,
                'september': 9, 'october': 10, 'november': 11, 'december': 12
            }
            
            for month_name, month_num in month_names.items():
                if month_name in user_input:
                    year = self.now.year
                    # If the month has already passed this year, assume next year
                    if month_num < self.now.month:
                        year += 1
                    return f"{year}-{month_num:02d}-01"
            
            # Try to parse with dateutil
            if DATETIME_LIBS_AVAILABLE:
                parsed_date = date_parser.parse(user_input, default=self.now)
                return parsed_date.strftime("%Y-%m-%d")
            else:
                # Simple fallback
                return self.now.strftime("%Y-%m-%d")
            
        except Exception as e:
            raise InputTransformationError(f"Could not parse move date '{user_input}': {e}")


class FlowInputTransformer:
    """Main class that orchestrates all input transformations."""
    
    def __init__(self, default_timezone: str = "America/Chicago"):
        self.datetime_transformer = DateTimeTransformer(default_timezone)
        self.phone_transformer = PhoneNumberTransformer()
        self.move_date_transformer = MoveDateTransformer()
    
    def transform_input(self, input_type: str, user_input: str) -> str:
        """
        Transform user input based on the specified type.
        
        Args:
            input_type: Type of transformation (datetime, phone_number, move_date)
            user_input: Raw user input
            
        Returns:
            Transformed input in required format
            
        Raises:
            InputTransformationError: If transformation fails
        """
        if input_type == "datetime":
            return self.datetime_transformer.transform_natural_datetime(user_input)
        elif input_type == "phone_number":
            return self.phone_transformer.transform_phone_number(user_input)
        elif input_type == "move_date":
            return self.move_date_transformer.transform_move_date(user_input)
        else:
            raise InputTransformationError(f"Unknown input type: {input_type}")
