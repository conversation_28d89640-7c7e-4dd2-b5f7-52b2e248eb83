"""
Flow Configuration Loader

This module loads and manages flow configurations for MCP tools,
providing AI agents with intelligent input handling guidance.
"""

import os
import yaml
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass
try:
    from .input_transformations import FlowInputTransformer, InputTransformationError
except ImportError:
    # Fallback for direct execution
    from input_transformations import FlowInputTransformer, InputTransformationError


@dataclass
class FlowConfiguration:
    """Represents a loaded flow configuration for an MCP tool."""
    tool_name: str
    version: str
    description: str
    role_prompt: str
    task_prompt: str
    input_transformations: Dict[str, Any]
    validation_rules: Dict[str, Any]
    error_handling: Dict[str, Any]
    user_experience_guidelines: Dict[str, Any]
    examples: Optional[Dict[str, Any]] = None


class FlowConfigurationLoader:
    """Loads and manages flow configurations for MCP tools."""
    
    def __init__(self, flows_directory: str = None):
        """
        Initialize the flow configuration loader.
        
        Args:
            flows_directory: Path to the flows directory. If None, uses default.
        """
        if flows_directory is None:
            # Default to flows directory relative to this file
            current_dir = Path(__file__).parent
            self.flows_directory = current_dir
        else:
            self.flows_directory = Path(flows_directory)
        
        self.loaded_flows: Dict[str, FlowConfiguration] = {}
        self.input_transformer = FlowInputTransformer()
    
    def load_flow_configuration(self, tool_name: str) -> Optional[FlowConfiguration]:
        """
        Load flow configuration for a specific tool.
        
        Args:
            tool_name: Name of the MCP tool
            
        Returns:
            FlowConfiguration object or None if not found
        """
        # Check if already loaded
        if tool_name in self.loaded_flows:
            return self.loaded_flows[tool_name]
        
        # Look for configuration file
        config_file = self.flows_directory / f"{tool_name}_flow.yaml"
        
        if not config_file.exists():
            return None
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # Validate required fields
            required_fields = [
                'tool_name', 'version', 'role_prompt', 'task_prompt',
                'input_transformations', 'validation_rules', 
                'error_handling', 'user_experience_guidelines'
            ]
            
            for field in required_fields:
                if field not in config_data:
                    raise ValueError(f"Missing required field '{field}' in {config_file}")
            
            # Create FlowConfiguration object
            flow_config = FlowConfiguration(
                tool_name=config_data['tool_name'],
                version=config_data['version'],
                description=config_data.get('description', ''),
                role_prompt=config_data['role_prompt'],
                task_prompt=config_data['task_prompt'],
                input_transformations=config_data['input_transformations'],
                validation_rules=config_data['validation_rules'],
                error_handling=config_data['error_handling'],
                user_experience_guidelines=config_data['user_experience_guidelines'],
                examples=config_data.get('examples')
            )
            
            # Cache the loaded configuration
            self.loaded_flows[tool_name] = flow_config
            return flow_config
            
        except Exception as e:
            print(f"Error loading flow configuration for {tool_name}: {e}")
            return None
    
    def get_enhanced_system_prompt(self, tool_name: str) -> str:
        """
        Get an enhanced system prompt that includes flow configuration guidance.
        
        Args:
            tool_name: Name of the MCP tool
            
        Returns:
            Enhanced system prompt with flow guidance
        """
        flow_config = self.load_flow_configuration(tool_name)
        
        if not flow_config:
            return "You are a helpful AI assistant."
        
        # Combine role prompt and task prompt
        enhanced_prompt = f"""
{flow_config.role_prompt}

TASK GUIDANCE:
{flow_config.task_prompt}

INPUT TRANSFORMATION CAPABILITIES:
You have access to intelligent input transformation for:
"""
        
        # Add transformation capabilities
        for transform_type, config in flow_config.input_transformations.items():
            enhanced_prompt += f"- {transform_type.upper()}: "
            if isinstance(config, dict) and 'patterns' in config:
                patterns = [p.get('pattern', '') for p in config['patterns']]
                enhanced_prompt += f"Can parse {', '.join(patterns[:3])}...\n"
            else:
                enhanced_prompt += f"Intelligent parsing available\n"
        
        # Add UX guidelines
        enhanced_prompt += f"""
USER EXPERIENCE PRINCIPLES:
{chr(10).join(f"- {principle}" for principle in flow_config.user_experience_guidelines.get('principles', []))}

CONVERSATION FLOW:
{chr(10).join(f"{i+1}. {step}" for i, step in enumerate(flow_config.user_experience_guidelines.get('conversation_flow', [])))}

CRITICAL: {chr(10).join(f"- {item}" for item in flow_config.user_experience_guidelines.get('do_not', []))}
"""
        
        return enhanced_prompt
    
    def transform_user_input(self, tool_name: str, input_type: str, user_input: str) -> str:
        """
        Transform user input using the flow configuration.
        
        Args:
            tool_name: Name of the MCP tool
            input_type: Type of input to transform (datetime, phone_number, etc.)
            user_input: Raw user input
            
        Returns:
            Transformed input in required format
            
        Raises:
            InputTransformationError: If transformation fails
        """
        flow_config = self.load_flow_configuration(tool_name)
        
        if not flow_config:
            raise InputTransformationError(f"No flow configuration found for tool: {tool_name}")
        
        # Use the input transformer
        return self.input_transformer.transform_input(input_type, user_input)
    
    def get_error_handling_guidance(self, tool_name: str, error_type: str) -> Optional[str]:
        """
        Get error handling guidance for a specific error type.
        
        Args:
            tool_name: Name of the MCP tool
            error_type: Type of error (ambiguous_datetime, invalid_phone, etc.)
            
        Returns:
            Error handling guidance or None if not found
        """
        flow_config = self.load_flow_configuration(tool_name)
        
        if not flow_config:
            return None
        
        strategies = flow_config.error_handling.get('strategies', [])
        
        for strategy in strategies:
            if strategy.get('type') == error_type:
                return strategy.get('example', strategy.get('action', ''))
        
        # Return a fallback prompt if available
        fallback_prompts = flow_config.error_handling.get('fallback_prompts', [])
        return fallback_prompts[0] if fallback_prompts else None
    
    def validate_tool_input(self, tool_name: str, input_data: Dict[str, Any]) -> List[str]:
        """
        Validate tool input against flow configuration rules.
        
        Args:
            tool_name: Name of the MCP tool
            input_data: Input data to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        flow_config = self.load_flow_configuration(tool_name)
        
        if not flow_config:
            return ["No flow configuration found"]
        
        errors = []
        required_fields = flow_config.validation_rules.get('required_fields', {})

        # Check required fields - handle both dict and list formats
        if isinstance(required_fields, dict):
            for field, description in required_fields.items():
                if field not in input_data or not input_data[field]:
                    errors.append(f"Missing required field: {field} ({description})")
        elif isinstance(required_fields, list):
            for field in required_fields:
                if field not in input_data or not input_data[field]:
                    errors.append(f"Missing required field: {field}")
        
        # Check format requirements
        format_requirements = flow_config.validation_rules.get('format_requirements', {})
        
        for field, format_desc in format_requirements.items():
            if field in input_data:
                value = input_data[field]
                # Add specific format validation logic here
                if field == 'phone' and not self._is_valid_phone_format(value):
                    errors.append(f"Invalid phone format: {value}")
                elif field == 'email' and not self._is_valid_email_format(value):
                    errors.append(f"Invalid email format: {value}")
        
        return errors
    
    def _is_valid_phone_format(self, phone: str) -> bool:
        """Check if phone number matches (XXX) XXX-XXXX format."""
        import re
        pattern = r'^\(\d{3}\) \d{3}-\d{4}$'
        return bool(re.match(pattern, phone))
    
    def _is_valid_email_format(self, email: str) -> bool:
        """Basic email format validation."""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    def list_available_flows(self) -> List[str]:
        """
        List all available flow configurations.
        
        Returns:
            List of tool names with available flow configurations
        """
        flow_files = list(self.flows_directory.glob("*_flow.yaml"))
        return [f.stem.replace("_flow", "") for f in flow_files]


# Global instance for easy access
flow_loader = FlowConfigurationLoader()
