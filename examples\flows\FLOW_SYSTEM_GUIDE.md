# Flow Configuration System - Complete Guide

## Overview

The Flow Configuration System is a revolutionary approach to improving AI agent UX by eliminating the friction of requiring users to provide perfectly formatted input. Instead of forcing users to reformat their natural language input, AI agents are taught to intelligently parse and transform user input automatically.

## The Problem We Solve

### Before Flow Configurations (Poor UX)
```
User: "I'd like to tour property 123 tomorrow afternoon"
AI: "Please provide the datetime in ISO format with timezone: YYYY-MM-DDTHH:MM:SS±HH:MM"
User: "I don't know what that means"
AI: "Please format your phone number as (XXX) XXX-XXXX"
User: "This is frustrating..."
```

### After Flow Configurations (Excellent UX)
```
User: "I'd like to tour property 123 tomorrow afternoon"
AI: "I'd be happy to help you schedule a tour of property 123 tomorrow afternoon. 
     I have you down for 2:00 PM tomorrow. To complete the scheduling, I'll need 
     your name, email, and phone number."
User: "<PERSON>, <EMAIL>, ************"
AI: "Perfect! I have <PERSON>, <EMAIL>, (*************. When are you 
     looking to move in?"
```

## System Architecture

### Components

1. **Flow Configuration Files** (`*.yaml`) - Define intelligent behavior for each MCP tool
2. **Input Transformation Utilities** (`input_transformations.py`) - Handle parsing and conversion
3. **Flow Configuration Loader** (`flow_loader.py`) - Load and manage configurations
4. **Enhanced AI Clients** - Integrate flow guidance into AI interactions

### Directory Structure
```
examples/flows/
├── README.md                              # Basic overview
├── FLOW_SYSTEM_GUIDE.md                  # This comprehensive guide
├── flow_loader.py                        # Configuration loader
├── input_transformations.py              # Transformation utilities
├── schedule_tour_flow.yaml               # Tour scheduling flow
├── pricing_and_availability_flow.yaml    # Pricing queries flow
└── get_property_available_times_flow.yaml # Available times flow
```

## Flow Configuration Schema

Each flow configuration file follows this structure:

```yaml
tool_name: "schedule_tour"
version: "1.0"
description: "Brief description of the flow"

# AI behavior guidance
role_prompt: |
  Instructions for how the AI should behave and handle user input

task_prompt: |
  Specific guidance for the task at hand

# Input transformation rules
input_transformations:
  datetime:
    patterns:
      - pattern: "tomorrow"
        transform: "Add 1 day to current date, default to 10:00 AM"
    output_format: "YYYY-MM-DDTHH:MM:SS-05:00"
    
  phone_number:
    patterns:
      - pattern: "(\\d{3})[-.\\s]?(\\d{3})[-.\\s]?(\\d{4})"
        transform: "($1) $2-$3"
    output_format: "(XXX) XXX-XXXX"

# Validation and error handling
validation_rules:
  required_fields:
    - property_id: "Must be a positive integer"
  format_requirements:
    datetime: "ISO format with timezone"

error_handling:
  strategies:
    - type: "ambiguous_datetime"
      action: "Ask for clarification in natural language"
      example: "Which day works best for you?"

# User experience guidelines
user_experience_guidelines:
  principles:
    - "Conversational and natural interaction"
    - "Intelligent input parsing"
  do_not:
    - "Ask users to reformat their input"
  do:
    - "Accept natural language input"
```

## Integration with AI Clients

### OpenAI Client Integration

The enhanced OpenAI client automatically:

1. **Loads Flow Configurations** - Discovers available flow configurations for MCP tools
2. **Enhances System Prompts** - Injects flow guidance into AI conversations
3. **Provides Tool Descriptions** - Adds intelligent input handling notes to tool descriptions
4. **Enables Natural Interaction** - Allows AI to handle user input intelligently

### Key Features

- **Automatic Discovery**: Finds and loads flow configurations for available tools
- **Enhanced Prompts**: Injects comprehensive guidance into system prompts
- **Tool Enhancement**: Adds natural language handling notes to tool descriptions
- **Graceful Fallback**: Works normally when flow configurations aren't available

## Input Transformation Capabilities

### DateTime Transformation
```python
# Natural language input → ISO format with timezone
"tomorrow afternoon" → "2025-07-21T14:00:00-05:00"
"next Tuesday at 2 PM" → "2025-07-23T14:00:00-05:00"
"morning" → "2025-07-20T10:00:00-05:00"
```

### Phone Number Transformation
```python
# Various formats → Standard format
"************" → "(*************"
"5551234567" → "(*************"
"************" → "(*************"
"****** 123 4567" → "(*************"
```

### Move Date Transformation
```python
# Natural language → YYYY-MM-DD format
"next month" → "2025-08-01"
"in 3 months" → "2025-10-01"
"spring" → "2025-03-01"
```

## Usage Examples

### Running the Enhanced Client

```bash
# Start the MCP server
uv run uvicorn main:app --host 0.0.0.0 --port 8000

# Run the enhanced OpenAI client
python examples/client_openai.py

# You'll see flow enhancement status:
# 🎯 Flow Enhancement: Enabled - AI will handle natural language input intelligently!
```

### Example Interactions

#### Tour Scheduling
```
User: I want to schedule a tour for property 123 tomorrow at 2 PM
AI: I'd be happy to schedule a tour for property 123 tomorrow at 2:00 PM. 
    I'll need a few details to complete the booking. What's your name and 
    contact information?

User: John Smith, <EMAIL>, ************
AI: Perfect! I have John Smith, <EMAIL>, (*************. 
    When are you looking to move in?

User: Next month
AI: Great! Let me schedule your tour for tomorrow at 2:00 PM for property 123, 
    with a target move-in date of August 1st, 2025. Does this look correct?
```

#### Pricing Queries
```
User: What's the pricing for a 2 bedroom at property 456? I need to move ASAP
AI: I'll check 2-bedroom pricing and availability at property 456 for 
    immediate move-in. Let me get that information for you.
    
    [Automatically transforms: property_id=456, bedrooms=2, move_date=today]
    
AI: Great news! Property 456 has 2-bedroom units available for immediate 
    move-in. Pricing ranges from $1,800-$2,200 depending on the floor and 
    specific unit features. Would you like to see the available options?
```

## Creating New Flow Configurations

### Step 1: Analyze the Tool
- Examine the tool's input schema and requirements
- Identify common user input patterns
- Determine transformation needs

### Step 2: Create the Configuration File
```bash
# Create new flow configuration
touch examples/flows/your_tool_flow.yaml
```

### Step 3: Define the Flow
```yaml
tool_name: "your_tool"
version: "1.0"
description: "Description of what this flow handles"

role_prompt: |
  Define how the AI should behave with this tool

task_prompt: |
  Specific guidance for this task

input_transformations:
  # Define transformation rules for common inputs

validation_rules:
  # Define validation requirements

error_handling:
  # Define error handling strategies

user_experience_guidelines:
  # Define UX principles and guidelines
```

### Step 4: Test the Flow
- Run the enhanced client
- Test various natural language inputs
- Verify transformations work correctly
- Refine the configuration as needed

## Best Practices

### Flow Configuration Design
1. **Be Comprehensive** - Cover all common input patterns
2. **Be Natural** - Focus on how users actually speak
3. **Be Forgiving** - Handle edge cases gracefully
4. **Be Clear** - Provide clear guidance to the AI

### Input Transformation
1. **Parse Liberally** - Accept many input formats
2. **Transform Consistently** - Always produce the same output format
3. **Validate Thoroughly** - Ensure transformed data is valid
4. **Error Gracefully** - Provide helpful error messages

### User Experience
1. **Eliminate Friction** - Never ask users to reformat input
2. **Be Conversational** - Maintain natural dialogue flow
3. **Confirm Understanding** - Verify before taking actions
4. **Provide Context** - Help users understand what's happening

## Troubleshooting

### Flow Configuration Not Loading
- Check file naming: `{tool_name}_flow.yaml`
- Verify YAML syntax is valid
- Ensure all required fields are present

### Transformations Not Working
- Check regex patterns in transformation rules
- Verify input_transformations.py is accessible
- Test transformations in isolation

### AI Not Following Flow Guidance
- Check system prompt injection
- Verify flow configuration is loaded
- Review role_prompt and task_prompt clarity

## Advanced Features

### Context-Aware Transformations
Some transformations can use conversation context:
```yaml
property_reference:
  patterns:
    - pattern: "the property|this property"
      transform: "Reference to previously mentioned property"
  context_required: true
```

### Conditional Logic
Transformations can include conditional logic:
```yaml
datetime:
  patterns:
    - pattern: "business hours"
      transform: "9:00 AM if weekday, 10:00 AM if weekend"
      conditions:
        - check_weekday: true
```

### Custom Validators
Add custom validation logic:
```python
def validate_business_hours(time_str: str) -> bool:
    # Custom validation logic
    return True
```

## Future Enhancements

### Planned Features
1. **Machine Learning Integration** - Learn from user patterns
2. **Multi-Language Support** - Handle different languages
3. **Voice Input Processing** - Parse speech-to-text input
4. **Context Memory** - Remember user preferences across sessions
5. **A/B Testing Framework** - Test different flow configurations

### Contributing
To contribute new flow configurations or improvements:
1. Create flow configurations for new tools
2. Enhance existing transformation utilities
3. Improve error handling strategies
4. Add new input transformation patterns
5. Enhance documentation and examples

## Conclusion

The Flow Configuration System transforms AI agent interactions from frustrating technical exchanges to natural, intelligent conversations. By teaching AI agents to handle user input intelligently, we eliminate UX friction and create delightful user experiences.

The system is designed to be:
- **Easy to Use** - Works automatically with existing MCP tools
- **Easy to Extend** - Simple to add new flow configurations
- **Easy to Maintain** - Clear structure and documentation
- **Easy to Test** - Comprehensive examples and validation

Start using flow configurations today to dramatically improve your AI agent's user experience!
